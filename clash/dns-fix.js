/**
 * Clash DNS配置修复工具
 * 针对域名解析失败问题提供多种DNS配置方案
 */

// DNS配置方案1: 保守模式 - 最大兼容性
const conservativeDNS = {
    "enable": true,
    "listen": "127.0.0.1:1053",
    "ipv6": false,
    "use-system-hosts": true,
    "cache-algorithm": "arc",
    "enhanced-mode": "redir-host", // 使用redir-host确保兼容性
    "fake-ip-range": "**********/16",
    "fake-ip-filter": [
        "+.lan", "+.local", "+.localhost", "+.corp", "+.home", "+.internal",
        "+.msftconnecttest.com", "+.msftncsi.com", 
        "+.microsoft.com", "+.microsoftonline.com", "+.office.com",
        "+.jetbrains.com", "+.intellij.net",
        "+.apple.com", "+.icloud.com",
        "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com"
    ],
    "default-nameserver": [
        "***************",   // 国内最稳定的DNS
        "*********",         // 阿里DNS
        "*******"            // Cloudflare备用
    ],
    "nameserver": [
        "***************",   // 优先使用传统DNS
        "*********",
        "************",
        "*******"            // 最后使用国外DNS
    ],
    "proxy-server-nameserver": [
        "*******",
        "*******"
    ],
    "nameserver-policy": {
        "geosite:private,cn,geolocation-cn": ["***************", "*********"],
        "geosite:gfw,geolocation-!cn": ["*******", "*******"]
    }
};

// DNS配置方案2: 平衡模式 - 速度与稳定性平衡
const balancedDNS = {
    "enable": true,
    "listen": "127.0.0.1:1053",
    "ipv6": false,
    "use-system-hosts": true,
    "cache-algorithm": "arc",
    "enhanced-mode": "redir-host",
    "fake-ip-range": "**********/16",
    "fake-ip-filter": [
        "+.lan", "+.local", "+.localhost", "+.corp", "+.home", "+.internal",
        "+.msftconnecttest.com", "+.msftncsi.com", 
        "+.microsoft.com", "+.microsoftonline.com", "+.office.com",
        "+.jetbrains.com", "+.intellij.net",
        "+.apple.com", "+.icloud.com",
        "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com"
    ],
    "default-nameserver": [
        "***************",
        "*********", 
        "*******",
        "*******"
    ],
    "nameserver": [
        "***************",
        "*********",
        "https://dns.alidns.com/dns-query",
        "*******",
        "https://*******/dns-query"
    ],
    "proxy-server-nameserver": [
        "*******",
        "*******",
        "https://*******/dns-query"
    ],
    "nameserver-policy": {
        "geosite:private,cn,geolocation-cn": [
            "***************",
            "*********",
            "https://dns.alidns.com/dns-query"
        ],
        "domain:microsoft.com,domain:microsoftonline.com,domain:office.com": [
            "*******", "*******"
        ],
        "domain:jetbrains.com,domain:intellij.net": [
            "*******", "*******"
        ],
        "geosite:gfw,geolocation-!cn": [
            "*******",
            "*******", 
            "https://*******/dns-query"
        ]
    }
};

// DNS配置方案3: 性能模式 - 最快解析速度
const performanceDNS = {
    "enable": true,
    "listen": "127.0.0.1:1053",
    "ipv6": false,
    "use-system-hosts": true,
    "cache-algorithm": "arc",
    "enhanced-mode": "fake-ip", // 使用fake-ip获得最佳性能
    "fake-ip-range": "**********/16",
    "fake-ip-filter": [
        "+.lan", "+.local", "+.localhost", "+.corp", "+.home", "+.internal",
        "+.msftconnecttest.com", "+.msftncsi.com", 
        "+.microsoft.com", "+.microsoftonline.com", "+.office.com",
        "+.jetbrains.com", "+.intellij.net",
        "+.apple.com", "+.icloud.com", "+.alipay.com", "+.paypal.com",
        "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com"
    ],
    "default-nameserver": [
        "***************",
        "*********"
    ],
    "nameserver": [
        "https://dns.alidns.com/dns-query",
        "https://doh.pub/dns-query",
        "https://*******/dns-query",
        "https://dns.google/dns-query"
    ],
    "proxy-server-nameserver": [
        "https://*******/dns-query",
        "https://dns.google/dns-query"
    ],
    "nameserver-policy": {
        "geosite:private,cn,geolocation-cn": [
            "https://dns.alidns.com/dns-query",
            "https://doh.pub/dns-query"
        ],
        "geosite:gfw,geolocation-!cn": [
            "https://*******/dns-query",
            "https://dns.google/dns-query"
        ]
    }
};

/**
 * 根据网络环境自动选择最佳DNS配置
 * @param {string} mode - 配置模式: 'conservative', 'balanced', 'performance'
 * @returns {Object} DNS配置对象
 */
function getDNSConfig(mode = 'balanced') {
    switch (mode.toLowerCase()) {
        case 'conservative':
            console.log('[DNS Fix] 使用保守模式DNS配置 - 最大兼容性');
            return conservativeDNS;
        case 'performance':
            console.log('[DNS Fix] 使用性能模式DNS配置 - 最快速度');
            return performanceDNS;
        case 'balanced':
        default:
            console.log('[DNS Fix] 使用平衡模式DNS配置 - 速度与稳定性平衡');
            return balancedDNS;
    }
}

/**
 * 检测当前网络环境并推荐最佳DNS配置
 * @returns {Object} 推荐的DNS配置和说明
 */
function detectAndRecommend() {
    // 简单的网络环境检测逻辑
    const recommendations = {
        mode: 'balanced',
        reason: '默认推荐平衡模式',
        alternatives: [
            {
                mode: 'conservative',
                when: '如果遇到应用连接问题或DNS解析失败',
                description: '使用传统DNS，最大兼容性'
            },
            {
                mode: 'performance', 
                when: '如果网络环境稳定且支持DoH',
                description: '使用DoH加密DNS，最佳性能'
            }
        ]
    };

    console.log('[DNS Fix] 网络环境检测完成');
    console.log(`[DNS Fix] 推荐模式: ${recommendations.mode}`);
    console.log(`[DNS Fix] 推荐理由: ${recommendations.reason}`);
    
    return {
        config: getDNSConfig(recommendations.mode),
        recommendations
    };
}

/**
 * 应用DNS修复到现有配置
 * @param {Object} clashConfig - 现有Clash配置
 * @param {string} mode - DNS模式
 * @returns {Object} 修复后的配置
 */
function applyDNSFix(clashConfig, mode = 'balanced') {
    const dnsConfig = getDNSConfig(mode);
    
    return Object.assign({}, clashConfig, {
        dns: dnsConfig
    });
}

// 导出功能
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getDNSConfig,
        detectAndRecommend,
        applyDNSFix,
        conservativeDNS,
        balancedDNS,
        performanceDNS
    };
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.DNSFix = {
        getDNSConfig,
        detectAndRecommend,
        applyDNSFix
    };
}
