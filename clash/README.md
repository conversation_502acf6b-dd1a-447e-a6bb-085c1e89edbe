# Clash 反向代理全局脚本 - 修复版

## 🎯 修复内容

### 主要问题修复

1. **恢复主函数功能** - 修复了被注释掉的核心逻辑
2. **DNS配置优化** - 统一并优化DNS解析策略
3. **代理组配置修复** - 解决配置冲突和不一致问题
4. **规则优先级优化** - 确保规则按正确顺序执行
5. **错误处理增强** - 添加完善的错误处理和日志

### 新增功能

1. **配置验证** - 自动验证用户配置的有效性
2. **诊断功能** - 提供配置问题诊断和建议
3. **详细日志** - 输出详细的执行日志便于调试
4. **性能优化** - 提升大量节点处理性能

## 🚀 使用方法

### 基本使用

```javascript
// 在 Clash 配置文件中引用脚本
function main(config) {
    // 脚本会自动处理配置
    return config;
}
```

### 自定义配置

修改 `userConfig` 对象来自定义配置：

```javascript
const userConfig = {
    // 自定义规则 - 优先级最高
    customRules: [
        "DOMAIN-SUFFIX,example.com,DIRECT",
        "IP-CIDR,***********/16,DIRECT,no-resolve"
    ],
    
    // 规则提供者配置
    ruleProviders: [
        { name: 'Google', icon: 'Google' },
        { name: 'YouTube', icon: 'YouTube' },
        { name: 'Microsoft', proxy: 'DIRECT' }
    ]
};
```

## 📋 配置说明

### DNS 配置优化 🔧

#### 核心改进
- **解析模式**: 改为 `redir-host` 提高兼容性
- **监听地址**: `127.0.0.1:1053` 提高安全性
- **IPv6**: 禁用避免双栈解析问题
- **系统hosts**: 启用支持本地解析
- **混合DNS**: 传统DNS + DoH 平衡速度与稳定性

#### DNS服务器配置
```javascript
"nameserver": [
    "***************",                    // 传统DNS - 快速
    "*********",                         // 传统DNS - 稳定
    "https://dns.alidns.com/dns-query",  // DoH - 安全
    "*******",                           // 国外DNS
    "https://*******/dns-query"         // 国外DoH
]
```

#### 分流策略优化
- **国内域名**: 使用国内DNS (***************, *********)
- **国外域名**: 使用国外DNS (*******, *******)
- **特殊域名**: Microsoft、JetBrains等使用专门配置

#### 三种DNS模式

1. **保守模式** - 最大兼容性
   - 仅使用传统DNS
   - 适合网络环境复杂的用户

2. **平衡模式** - 推荐使用 ⭐
   - 传统DNS + DoH混合
   - 平衡速度与稳定性

3. **性能模式** - 最快速度
   - 主要使用DoH
   - 适合网络环境稳定的用户

### 规则提供者配置

支持三种配置格式：

1. **默认规则**: `{ name: 'Google' }`
2. **自定义代理**: `{ name: 'Microsoft', proxy: 'DIRECT' }`
3. **自定义路径**: `{ name: 'GitHub', rules: 'GitHub/GitHub.yaml' }`

### 代理组配置

自动生成以下代理组：

- **自动选择**: 基于延迟自动选择最优节点
- **区域分组**: 按地理位置分组节点
- **规则分组**: 为每个规则提供者创建对应分组
- **特殊分组**: 全球直连、广告拦截、漏网之鱼

## 🔧 故障排除

### DNS解析失败问题 🚨

**症状**: 日志中出现大量 "dns resolve failed" 或连接超时错误

**解决方案**:

1. **切换DNS模式**
   ```javascript
   // 在clash.js中修改DNS配置
   "enhanced-mode": "redir-host"  // 改为redir-host模式
   ```

2. **使用DNS修复工具**
   ```javascript
   // 加载DNS修复工具
   const DNSFix = require('./dns-fix.js');

   // 应用保守模式DNS配置
   const fixedConfig = DNSFix.applyDNSFix(config, 'conservative');
   ```

3. **手动优化DNS配置**
   - 禁用IPv6: `"ipv6": false`
   - 增加传统DNS: `"***************", "*********"`
   - 调整超时时间: `"timeout": 8000`

### 常见问题

1. **特定域名解析失败** (如Microsoft、JetBrains)
   - 已在自定义规则中添加直连规则
   - 检查是否被错误代理
   - 尝试清除DNS缓存

2. **订阅服务无法访问**
   - 检查代理组配置
   - 确认规则优先级
   - 查看日志输出

3. **节点连接超时**
   - 增加超时时间到8秒以上
   - 检查测速URL可用性
   - 启用懒加载减少连接

### 调试方法

1. **查看日志输出**
```javascript
// 在浏览器控制台查看详细日志
console.log('[Clash Script] 配置生成完成');
```

2. **使用诊断功能**
```javascript
// 获取配置诊断信息
const diagnosis = main.diagnose(config);
console.log(diagnosis);
```

3. **运行测试脚本**
```bash
node clash/test.js
```

## 📊 性能优化

### 优化措施

1. **图标缓存**: 预加载常用图标减少重复请求
2. **配置合并**: 优化对象合并算法
3. **规则去重**: 自动过滤重复规则
4. **错误恢复**: 出错时返回原配置保证基本功能

### 性能指标

- **节点处理**: 支持100+节点快速处理
- **规则生成**: 毫秒级规则生成速度
- **内存占用**: 优化内存使用避免泄漏

## 🛡️ 安全考虑

### 安全措施

1. **输入验证**: 严格验证所有输入参数
2. **错误处理**: 防止敏感信息泄露
3. **DNS安全**: 使用DoH加密DNS查询
4. **本地监听**: DNS服务仅本地访问

### 隐私保护

- 不收集用户数据
- 不上传配置信息
- 本地处理所有逻辑

## 📝 更新日志

### v2.1.0 (当前版本) - DNS优化版 🚀

**重大改进**:
- 🔧 **深度DNS优化**: 解决大量域名解析失败问题
- 🌐 **切换redir-host模式**: 提高应用兼容性
- ⚡ **混合DNS策略**: 传统DNS + DoH平衡速度与稳定性
- 🛡️ **禁用IPv6**: 避免双栈解析问题
- ⏱️ **增加超时时间**: 从3秒增加到8-10秒
- 📋 **扩展直连规则**: 特别优化Microsoft、JetBrains等服务
- 🔍 **DNS诊断工具**: 自动检测和修复DNS问题
- 🚀 **快速修复脚本**: 一键解决常见DNS问题

**修复的具体问题**:
- ✅ Microsoft服务连接失败
- ✅ JetBrains下载超时
- ✅ Augment API访问问题
- ✅ 各种"dns resolve failed"错误
- ✅ 代理组测速失败

### v2.0.0 (前一版本)

- ✅ 修复主函数被禁用问题
- ✅ 优化DNS配置策略
- ✅ 增强错误处理机制
- ✅ 添加配置验证功能
- ✅ 新增诊断工具
- ✅ 完善日志输出
- ✅ 提升性能表现

### v1.0.0 (原始版本)

- 基础配置生成功能
- 区域节点分组
- 规则提供者支持

## 🤝 贡献指南

欢迎提交问题和改进建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🔗 相关链接

- [Clash 官方文档](https://clash.wiki/)
- [规则集合项目](https://github.com/blackmatrix7/ios_rule_script)
- [图标集合](https://www.clashverge.dev/guide/group_icon/icon_sets/Qure.html)
