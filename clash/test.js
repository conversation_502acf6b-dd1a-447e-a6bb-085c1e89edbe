/**
 * Clash配置脚本测试文件
 * 用于验证修复后的功能
 */

// 模拟Clash环境
const fs = require('fs');
const path = require('path');

// 加载主脚本
const clashScript = fs.readFileSync(path.join(__dirname, 'clash.js'), 'utf8');

// 在沙箱环境中执行脚本
function runClashScript(config) {
    const sandbox = {
        console: console,
        // 模拟main函数
        main: null
    };
    
    // 执行脚本
    const vm = require('vm');
    const context = vm.createContext(sandbox);
    vm.runInContext(clashScript, context);
    
    return context.main;
}

// 测试用例
function runTests() {
    console.log('🧪 开始测试 Clash 配置脚本...\n');
    
    try {
        // 加载脚本
        const main = runClashScript();
        
        // 测试1: 空配置
        console.log('📋 测试1: 空配置处理');
        const result1 = main({});
        console.log('✅ 空配置测试通过\n');
        
        // 测试2: 基本配置
        console.log('📋 测试2: 基本配置处理');
        const basicConfig = {
            proxies: [
                { name: '香港节点1', server: 'hk1.example.com', port: 443 },
                { name: '美国节点1', server: 'us1.example.com', port: 443 },
                { name: '日本节点1', server: 'jp1.example.com', port: 443 }
            ]
        };
        const result2 = main(basicConfig);
        console.log('✅ 基本配置测试通过\n');
        
        // 测试3: 诊断功能
        console.log('📋 测试3: 配置诊断功能');
        const diagnosis = main.diagnose(basicConfig);
        console.log('诊断结果:', diagnosis);
        console.log('✅ 诊断功能测试通过\n');
        
        // 测试4: 验证生成的配置结构
        console.log('📋 测试4: 验证配置结构');
        const requiredFields = ['dns', 'rules', 'proxy-groups', 'rule-providers'];
        const missingFields = requiredFields.filter(field => !result2[field]);
        
        if (missingFields.length === 0) {
            console.log('✅ 配置结构完整');
        } else {
            console.log('❌ 缺少字段:', missingFields);
        }
        
        // 测试5: DNS配置验证
        console.log('\n📋 测试5: DNS配置验证');
        const dnsConfig = result2.dns;
        if (dnsConfig && dnsConfig.enable && dnsConfig.nameserver && dnsConfig.nameserver.length > 0) {
            console.log('✅ DNS配置正确');
        } else {
            console.log('❌ DNS配置有问题');
        }
        
        // 测试6: 规则数量验证
        console.log('\n📋 测试6: 规则数量验证');
        const rules = result2.rules;
        if (rules && Array.isArray(rules) && rules.length > 0) {
            console.log(`✅ 生成了 ${rules.length} 条规则`);
        } else {
            console.log('❌ 规则生成失败');
        }
        
        // 测试7: 代理组验证
        console.log('\n📋 测试7: 代理组验证');
        const proxyGroups = result2['proxy-groups'];
        if (proxyGroups && Array.isArray(proxyGroups) && proxyGroups.length > 0) {
            console.log(`✅ 生成了 ${proxyGroups.length} 个代理组`);
            
            // 检查必要的代理组
            const requiredGroups = ['自动选择', '全球直连', '广告拦截', '漏网之鱼'];
            const existingGroups = proxyGroups.map(g => g.name);
            const missingGroups = requiredGroups.filter(name => !existingGroups.includes(name));
            
            if (missingGroups.length === 0) {
                console.log('✅ 必要代理组完整');
            } else {
                console.log('⚠️  缺少代理组:', missingGroups);
            }
        } else {
            console.log('❌ 代理组生成失败');
        }
        
        console.log('\n🎉 所有测试完成!');
        
        // 输出配置摘要
        console.log('\n📊 配置摘要:');
        console.log(`- 代理节点: ${basicConfig.proxies.length} 个`);
        console.log(`- 生成规则: ${result2.rules ? result2.rules.length : 0} 条`);
        console.log(`- 代理组: ${result2['proxy-groups'] ? result2['proxy-groups'].length : 0} 个`);
        console.log(`- 规则提供者: ${result2['rule-providers'] ? Object.keys(result2['rule-providers']).length : 0} 个`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error.stack);
    }
}

// 性能测试
function performanceTest() {
    console.log('\n⚡ 性能测试...');
    
    try {
        const main = runClashScript();
        
        // 创建大量代理节点进行测试
        const largeConfig = {
            proxies: []
        };
        
        // 生成100个测试节点
        for (let i = 1; i <= 100; i++) {
            largeConfig.proxies.push({
                name: `测试节点${i}`,
                server: `test${i}.example.com`,
                port: 443
            });
        }
        
        const startTime = Date.now();
        const result = main(largeConfig);
        const endTime = Date.now();
        
        console.log(`✅ 处理 ${largeConfig.proxies.length} 个节点耗时: ${endTime - startTime}ms`);
        
    } catch (error) {
        console.error('❌ 性能测试失败:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    runTests();
    performanceTest();
}

module.exports = {
    runTests,
    performanceTest,
    runClashScript
};
