// ==================== 用户配置区域 ====================
/**
 * Clash配置生成器 - 简化版
 *
 * 配置说明:
 * 1. rules - 字符串格式:
 *    - 不指定: 使用默认规则 (name.yaml)
 *    - 以.yaml结尾: 自定义路径 (如: "GitHub/GitHub.yaml")
 *    - 其他字符串: 规则变体 (如: "Classical")
 *
 * 2. proxy - 字符串格式:
 *    - "DIRECT": 直连
 *    - "REJECT": 拒绝连接
 *    - 区域节点名称: 使用特定区域节点 (如: "香港节点")
 *    - 其他字符串: 使用指定的代理组
 *
 * 3. icon - 图标配置:
 *    - 字符串名称: 使用预定义图标 (如: "GitHub")
 *    - URL: 使用自定义图标
 */

/**
 * @typedef {Object} RuleProvider
 * @property {string} name - 规则名称
 * @property {string} [icon] - 分组图标
 * @property {string} [proxy] - 代理设置，支持 "DIRECT"、"REJECT" 或分组名称
 * @property {string} [rules] - 规则配置，支持以下格式:
 *   - 不指定: 使用默认规则 (name.yaml)
 *   - 以 .yaml 结尾: 视为自定义路径
 *   - 其他字符串: 视为规则变体名称 (name_variant.yaml)
 */

/**
 * @typedef {Object} ProxyGroupOptions
 * @property {number} [interval] - 测速间隔(秒)
 * @property {number} [timeout] - 连接超时(毫秒)
 * @property {string} [url] - 测速URL
 * @property {boolean} [lazy] - 是否懒加载
 */

/**
 * @typedef {Object} UserConfig
 * @property {string[]} [customRules] - 自定义规则，优先级最高
 * @property {string} [excludeRegex] - 需要排除的节点名称正则
 * @property {RuleProvider[]} [ruleProviders] - 规则提供者配置
 * @property {ProxyGroupOptions} [proxyGroupOptions] - 代理组通用配置
 * @property {string} [iconCDN] - 图标CDN配置
 * @property {Object} [dns] - DNS配置
 */

/**
 * 用户配置对象
 * @type {UserConfig}
 */
const userConfig = {
    // 自定义规则 - 优先级最高
    customRules: [
        // 内网IP段直连
        "IP-CIDR,10.0.0.0/8,DIRECT,no-resolve",
        "IP-CIDR,**********/12,DIRECT,no-resolve",
        "IP-CIDR,***********/16,DIRECT,no-resolve",
        "IP-CIDR,*********/8,DIRECT,no-resolve",
        "IP-CIDR,***********/16,DIRECT,no-resolve",

        // 特定服务直连
        "DOMAIN-SUFFIX,download.jetbrains.com,DIRECT",

        // 白名单域名直连
        ...createWhiteDomainList([
            "clivia.fun",
            "baidu.com",
            "360.net",
            "hf.space",
            "linux.do",
            "pages.dev",
            "nbcharts.com",
            "api.augmentcode.com",
            "workers.dev",
            "deno.dev",
            "cursor.sh",
            "cursor-cdn.com",
            "cursorapi.com",
            "cursor.com",
            "workos.com"
        ])
    ],

    /**
     * 规则提供者配置
     * 规则名称参考： https://github.com/blackmatrix7/ios_rule_script/tree/master/rule/Clash#分类
     * 图标获取参考： https://www.clashverge.dev/guide/group_icon/icon_sets/Qure.html
     *
     * @type {RuleProvider[]}
     */
    ruleProviders: [
        // 中国大陆网站直连
        { name: 'ChinaMax', icon: 'China', rules: 'Classical', proxy: 'DIRECT' },

        // 国内服务直连
        { name: 'Microsoft', icon: 'Microsoft', proxy: 'DIRECT' },
        { name: '360', icon: 'Ninja', proxy: 'DIRECT' },

        // 需要代理的服务
        { name: 'Google', icon: 'Google' },
        { name: 'YouTube', icon: 'YouTube' },
        { name: 'Telegram', icon: 'Telegram' },

        // AI服务使用非港澳节点
        { name: 'Gemini', icon: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png', proxy: '非港澳节点' },

        // 开发相关服务
        { name: 'GitHub', icon: 'GitHub', rules: 'GitHub/GitHub.yaml' },

        // Cloudflare服务 - 修复为代理访问
        { name: 'Cloudflare', icon: 'Cloudflare' }
    ]
};

// ==================== 常量定义 ====================
const CONSTANTS = {
    // 区域正则定义
    REGION_REGEX: {
        HONG_KONG: {
            name: "香港节点",
            regex: /港|HK|hk|Hong Kong|HongKong|hongkong|Hongkong|🇭🇰/u,
            icon: "Hong_Kong"
        },
        TAIWAN: {
            name: "台湾节点",
            regex: /台|新北|彰化|TW|Taiwan/u,
            icon: "TW"
        },
        US: {
            name: "美国节点",
            regex: /美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States/u,
            icon: "United_States"
        },
        JAPAN: {
            name: "日本节点",
            regex: /日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan|Tokyo/u,
            icon: "Japan"
        },
        SINGAPORE: {
            name: "狮城节点",
            regex: /新加坡|坡|狮城|SG|Singapore/u,
            icon: "Singapore"
        },
        NON_HK_MO: {
            name: "非港澳节点",
            regex: /^(?!.*(?:🇭🇰|🇲🇴|港|澳|hk|mo|hong kong|macau)).*/ui,
            icon: "Area"
        }
    },

    // 常用代理组名称
    PROXY_GROUPS: {
        AUTO_SELECT: "自动选择",
        GLOBAL_DIRECT: "全球直连",
        AD_BLOCK: "广告拦截",
        FINAL: "漏网之鱼"
    },

    // 规则提供者基础URL
    RULE_PROVIDER_BASE_URL: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash"
};

// ==================== 默认配置 ====================
const defaultConfig = {
    // 自定义规则 - 优先级最高
    customRules: [],

    // 需要排除的节点名称正则
    excludeRegex: "(?!.*(官网|网站|流量|套餐|到期|剩余|更新|产品|官方|客服|联系|邮箱|工单|群|频道|公告|推荐|返利|充值|付费|提示|警告|维护|测试|过期|失效)).*",

    // 规则提供者配置
    ruleProviders: [],

    // 代理组通用配置
    proxyGroupOptions: {
        interval: 300,
        timeout: 3000,
        url: "https://www.google.com/generate_204",
        // lazy: true
    },

    // 图标CDN配置
    iconCDN: "https://fastly.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color",

    // DNS配置 - 针对解析失败问题的深度优化
    dns: {
        "enable": true,
        "listen": "127.0.0.1:1053",
        "ipv6": false, // 禁用IPv6避免双栈解析问题
        "use-system-hosts": true,
        "cache-algorithm": "arc",
        "enhanced-mode": "redir-host", // 改为redir-host模式，避免fake-ip兼容性问题
        "fake-ip-range": "**********/16",
        "fake-ip-filter": [
            // 本地网络和内网域名
            "+.lan", "+.local", "+.localhost", "+.corp", "+.home", "+.internal",
            // 微软服务 - 避免连接测试问题
            "+.msftconnecttest.com", "+.msftncsi.com",
            "+.microsoft.com", "+.microsoftonline.com", "+.office.com",
            // 腾讯服务
            "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com",
            // 开发工具和IDE
            "+.jetbrains.com", "+.intellij.net",
            // 其他可能需要真实IP的服务
            "+.apple.com", "+.icloud.com",
            // 银行和支付服务
            "+.alipay.com", "+.paypal.com"
        ],
        // 默认DNS - 使用更快的国内DNS
        "default-nameserver": [
            "***************",   // 114DNS - 稳定快速
            "*********",         // 阿里DNS
            "************",      // 腾讯DNS
            "*******"            // Cloudflare备用
        ],
        // 主DNS服务器 - 混合DoH和传统DNS提高成功率
        "nameserver": [
            "***************",                      // 传统DNS - 快速
            "*********",                           // 传统DNS - 稳定
            "https://dns.alidns.com/dns-query",    // DoH - 安全
            "https://doh.pub/dns-query",           // DoH - 备用
            "*******",                             // 国外传统DNS
            "https://*******/dns-query"           // 国外DoH
        ],
        // 代理服务器DNS - 优先使用稳定的国外DNS
        "proxy-server-nameserver": [
            "*******",                             // Cloudflare传统DNS
            "8.8.8.8",                             // Google传统DNS
            "https://*******/dns-query",          // Cloudflare DoH
            "https://dns.google/dns-query"         // Google DoH
        ],
        // DNS分流策略 - 更精确的域名分类
        "nameserver-policy": {
            // 国内域名和服务 - 使用国内DNS
            "geosite:private,cn,geolocation-cn": [
                "***************",
                "*********",
                "https://dns.alidns.com/dns-query"
            ],
            // 特定国内服务域名
            "domain:baidu.com,domain:qq.com,domain:taobao.com,domain:tmall.com,domain:jd.com": [
                "***************",
                "*********"
            ],
            // 微软服务 - 使用混合DNS确保连通性
            "domain:microsoft.com,domain:microsoftonline.com,domain:office.com,domain:live.com": [
                "*******",
                "8.8.8.8",
                "https://*******/dns-query"
            ],
            // JetBrains服务
            "domain:jetbrains.com,domain:intellij.net": [
                "*******",
                "8.8.8.8",
                "https://*******/dns-query"
            ],
            // 开发相关服务
            "domain:github.com,domain:gitlab.com,domain:augmentcode.com": [
                "*******",
                "8.8.8.8",
                "https://*******/dns-query"
            ],
            // 国外域名和GFW列表 - 使用国外DNS
            "geosite:google,youtube,telegram,gfw,geolocation-!cn": [
                "*******",
                "8.8.8.8",
                "https://*******/dns-query",
                "https://dns.google/dns-query"
            ],
            // Cloudflare服务
            "geosite:cloudflare": [
                "*******",
                "https://*******/dns-query"
            ]
        }
    }
};

// ==================== 工具函数 ====================
/**
 * 创建白名单域名列表
 * @param {string[]} domainList - 域名列表
 * @returns {string[]} - 白名单域名列表
 */
function createWhiteDomainList(domainList) {
    return domainList.map(domain => `DOMAIN-SUFFIX,${domain},DIRECT`);
}

/**
 * 工具函数集合
 */
const utils = {
    /**
     * 将任何值标准化为数组
     * @param {*} value - 输入值
     * @return {Array} 标准化后的数组
     */
    toArray(value) {
        return value ? (Array.isArray(value) ? value : [value]) : [];
    },

    /**
     * 获取代理动作
     * @param {string} proxy - 代理设置
     * @param {string} groupName - 分组名称
     * @return {string} - 处理后的代理配置
     */
    getProxyAction(proxy, groupName) {
        // 如果未指定代理，使用组名
        if (proxy === undefined || proxy === null) {
            return groupName;
        }

        // 只处理字符串类型
        if (typeof proxy === 'string') {
            const proxyValue = proxy.trim();
            // 如果是空字符串，返回DIRECT
            if (proxyValue === '') {
                return 'DIRECT';
            }
            return proxyValue;
        }

        // 默认返回组名
        return groupName;
    },

    /**
     * 判断是否为URL
     * @param {string} url - 待检测的字符串
     * @returns {boolean} 是否为URL
     */
    isUrl(url) {
        if (typeof url !== 'string') return false;
        return /^https?:\/\//i.test(url);
    },

    /**
     * 判断是否为对象
     * @param {*} item - 待检测的项
     * @returns {boolean} 是否为对象
     */
    isObject(item) {
        return (item && typeof item === 'object' && !Array.isArray(item));
    },

    /**
     * 合并对象，深度合并
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     */
    mergeObjects(target, source) {
        if (!source) return target;
        if (!this.isObject(source)) return source;

        if (!this.isObject(target)) {
            return JSON.parse(JSON.stringify(source));
        }

        const merged = Object.assign({}, target);

        Object.keys(source).forEach(key => {
            const sourceValue = source[key];

            if (this.isObject(sourceValue) && !Array.isArray(sourceValue)) {
                if (!target[key] || !this.isObject(target[key])) {
                    merged[key] = JSON.parse(JSON.stringify(sourceValue));
                } else {
                    merged[key] = this.mergeObjects(target[key], sourceValue);
                }
            }
            else if (Array.isArray(sourceValue)) {
                merged[key] = JSON.parse(JSON.stringify(sourceValue));
            }
            else {
                merged[key] = sourceValue;
            }
        });

        return merged;
    },

    /**
     * 编译正则表达式
     * @param {string|RegExp} pattern - 正则表达式或模式字符串
     * @param {string} [flags] - 正则表达式标志
     * @returns {RegExp} 编译后的正则表达式
     */
    compileRegex(pattern, flags) {
        if (pattern instanceof RegExp) {
            return pattern;
        }

        if (typeof pattern !== 'string') {
            return new RegExp('.*');
        }

        try {
            return new RegExp(pattern, flags);
        } catch (e) {
            return new RegExp('.*');
        }
    }
};

/**
 * 图标管理类
 */
class IconManager {
    /**
     * 创建图标管理器
     * @param {UserConfig} config - 用户配置
     */
    constructor(config) {
        this.config = config || {};
        this.iconCache = new Map();
        this.defaultIcon = `${this.config.iconCDN || defaultConfig.iconCDN}/Global.png`;
    }

    /**
     * 获取图标URL
     * @param {string} iconName - 图标名称
     * @returns {string} 图标URL
     */
    getIcon(iconName) {
        if (iconName === null || iconName === undefined) {
            return this.defaultIcon;
        }

        const cacheKey = String(iconName);
        if (this.iconCache.has(cacheKey)) {
            return this.iconCache.get(cacheKey);
        }

        let iconUrl;
        if (utils.isUrl(iconName)) {
            iconUrl = iconName;
        } else if (!iconName) {
            iconUrl = this.defaultIcon;
        } else {
            const iconCDN = this.config.iconCDN || defaultConfig.iconCDN;
            iconUrl = `${iconCDN}/${iconName}.png`;
        }

        this.iconCache.set(cacheKey, iconUrl);

        return iconUrl;
    }

    /**
     * 预加载常用图标到缓存
     */
    preloadCommonIcons() {
        const commonIcons = ['Direct', 'AdBlack', 'Final', 'Global', 'Auto'];
        commonIcons.forEach(name => {
            this.getIcon(name);
        });

        Object.values(CONSTANTS.REGION_REGEX).forEach(region => {
            this.getIcon(region.icon);
        });
    }
}

/**
 * 规则管理类
 */
class RuleManager {
    /**
     * 创建规则管理器
     * @param {UserConfig} config - 用户配置
     * @param {IconManager} iconManager - 图标管理器
     */
    constructor(config, iconManager) {
        this.config = config || {};
        this.iconManager = iconManager;
    }

    /**
     * 获取规则文件URL
     * @param {string} name - 规则名称
     * @param {string} rules - 规则配置
     * @returns {string} 规则文件URL
     * @private
     */
    _getRuleUrl(name, rules) {
        const baseUrl = CONSTANTS.RULE_PROVIDER_BASE_URL;

        // 如果未提供rules，使用默认规则
        if (!rules) {
            return `${baseUrl}/${name}/${name}.yaml`;
        }

        // 如果rules以.yaml结尾，视为自定义路径
        if (typeof rules === 'string' && rules.endsWith('.yaml')) {
            return `${baseUrl}/${rules}`;
        }

        // 其他字符串视为变体名称
        if (typeof rules === 'string') {
            return `${baseUrl}/${name}/${name}_${rules}.yaml`;
        }

        // 默认返回
        return `${baseUrl}/${name}/${name}.yaml`;
    }

    /**
     * 获取规则文件本地路径
     * @param {string} name - 规则名称
     * @param {string} rules - 规则配置
     * @returns {string} 本地路径
     * @private
     */
    _getLocalPath(name, rules) {
        // 如果未提供rules，使用默认规则
        if (!rules) {
            return `./ruleset/tnnevol/${name}.yaml`;
        }

        // 如果rules以.yaml结尾，提取文件名部分
        if (typeof rules === 'string' && rules.endsWith('.yaml')) {
            const parts = rules.split('/');
            return `./ruleset/tnnevol/${parts[parts.length - 1]}`;
        }

        // 其他字符串视为变体名称
        if (typeof rules === 'string') {
            return `./ruleset/tnnevol/${name}_${rules}.yaml`;
        }

        // 默认返回
        return `./ruleset/tnnevol/${name}.yaml`;
    }

    /**
     * 创建规则提供者配置
     * @returns {Object} 规则提供者配置对象
     */
    createRuleProviders() {
        const providers = {};
        const ruleProviders = this.config.ruleProviders || [];

        ruleProviders.forEach(({ name, icon, rules }) => {
            if (!name) return;

            providers[name] = {
                type: "http",
                format: "yaml",
                interval: 86400,
                behavior: "classical",
                url: this._getRuleUrl(name, rules),
                icon: this.iconManager.getIcon(icon || name),
                path: this._getLocalPath(name, rules)
            };
        });

        return providers;
    }

    /**
     * 创建规则列表
     * @returns {string[]} 规则列表
     */
    createRules() {
        const ruleProviders = this.config.ruleProviders || [];
        const rules = [];

        for (let i = 0; i < ruleProviders.length; i++) {
            const item = ruleProviders[i];
            if (!item || !item.name) continue;

            const action = utils.getProxyAction(item.proxy, item.name);
            rules.push(`RULE-SET,${item.name},${action}`);
        }

        return rules;
    }

    /**
     * 生成完整规则列表
     * @returns {string[]} 完整规则列表
     */
    generateRules() {
        try {
            const pgNames = CONSTANTS.PROXY_GROUPS;
            const customRules = this.config.customRules || [];
            const ruleProviderRules = this.createRules();

            // 构建规则列表，确保优先级正确
            const rules = [
                // 1. 自定义规则 - 最高优先级
                ...customRules,

                // 2. 规则提供者规则 - 中等优先级
                ...ruleProviderRules,

                // 3. 地理位置规则 - 较低优先级
                `GEOSITE,cn,${pgNames.GLOBAL_DIRECT}`,
                `GEOSITE,google-cn,${pgNames.GLOBAL_DIRECT}`,
                `GEOSITE,category-games@cn,${pgNames.GLOBAL_DIRECT}`,
                `GEOSITE,category-public-tracker,${pgNames.GLOBAL_DIRECT}`,

                // 4. GFW规则 - 需要代理的网站
                `GEOSITE,gfw,${pgNames.AUTO_SELECT}`,

                // 5. IP规则 - 基于IP的路由
                `GEOIP,LAN,${pgNames.GLOBAL_DIRECT},no-resolve`,
                `GEOIP,CN,${pgNames.GLOBAL_DIRECT},no-resolve`,

                // 6. 兜底规则 - 最低优先级
                `MATCH,${pgNames.FINAL}`
            ];

            // 过滤空规则和重复规则
            const filteredRules = rules.filter((rule, index, arr) =>
                rule && typeof rule === 'string' && rule.trim() !== '' &&
                arr.indexOf(rule) === index
            );

            console.log(`[Rule Manager] 生成了 ${filteredRules.length} 条有效规则`);
            return filteredRules;

        } catch (error) {
            console.error('[Rule Manager] 规则生成失败:', error.message);
            return [];
        }
    }
}

/**
 * 代理组管理类
 */
class ProxyGroupManager {
    /**
     * 创建代理组管理器
     * @param {UserConfig} config - 用户配置
     * @param {IconManager} iconManager - 图标管理器
     */
    constructor(config, iconManager) {
        this.config = config || {};
        this.iconManager = iconManager;
        this.excludeRegex = utils.compileRegex(this.config.excludeRegex || "", "u");
        this._regionDefinitions = Object.values(CONSTANTS.REGION_REGEX);
    }

    /**
     * 获取符合正则表达式的代理列表
     * @param {Array} proxies - 代理列表
     * @param {string|RegExp} regexStr - 正则表达式或模式
     * @param {Array} [concatProxies=[]] - 要合并的额外代理
     * @returns {Array} 筛选后的代理列表
     */
    getProxiesByRegex(proxies, regexStr, concatProxies = []) {
        if (!proxies || !Array.isArray(proxies)) {
            return [...concatProxies];
        }

        const regex = utils.compileRegex(regexStr, "u");
        const filteredProxies = [];

        for (let i = 0; i < proxies.length; i++) {
            const proxy = proxies[i];
            if (proxy && proxy.name &&
                regex.test(proxy.name) &&
                this.excludeRegex.test(proxy.name)) {
                filteredProxies.push(proxy.name);
            }
        }

        if (concatProxies.length > 0) {
            filteredProxies.push(...concatProxies);
        }

        return filteredProxies;
    }

    /**
     * 创建区域分组
     * @param {Array} proxies - 代理列表
     * @returns {Array} 区域分组列表
     */
    createRegionGroups(proxies) {
        if (!proxies || !Array.isArray(proxies)) {
            proxies = [];
        }

        const regionGroups = [];

        for (let i = 0; i < this._regionDefinitions.length; i++) {
            const region = this._regionDefinitions[i];

            const regionProxies = this.getProxiesByRegex(
                proxies,
                region.regex
            );

            if (regionProxies.length === 0) continue;

            regionGroups.push({
                ...this.config.proxyGroupOptions,
                name: region.name,
                type: "url-test",
                tolerance: 30,
                proxies: regionProxies,
                icon: this.iconManager.getIcon(region.icon)
            });
        }

        return regionGroups;
    }

    /**
     * 创建基础代理组
     * @param {Array} regionGroups - 区域分组
     * @returns {Array} 代理组列表
     */
    createBaseProxyGroups(regionGroups) {
        if (!regionGroups) {
            regionGroups = [];
        }

        const pgNames = CONSTANTS.PROXY_GROUPS;
        const regionGroupNames = regionGroups.map(group => group.name);

        // 简化的公共代理列表，只包含自动选择和区域组
        const commonProxies = [
            pgNames.AUTO_SELECT,
            ...regionGroupNames,
            "DIRECT",
        ];

        // 简化的基础分组，只保留自动选择
        const baseGroups = [
            {
                ...this.config.proxyGroupOptions,
                name: pgNames.AUTO_SELECT,
                type: "url-test",
                tolerance: 100,
                filter: this.config.excludeRegex,
                "include-all": true,
                icon: this.iconManager.getIcon("Auto")
            }
        ];

        const ruleProviders = this.config.ruleProviders || [];
        const ruleGroups = [];

        for (let i = 0; i < ruleProviders.length; i++) {
            const item = ruleProviders[i];
            if (!item || !item.name) continue;

            // 根据proxy设置决定代理列表
            let proxyList;

            if (item.proxy === "DIRECT") {
                proxyList = ["DIRECT"];
            } else if (item.proxy === "REJECT") {
                proxyList = ["REJECT"];
            } else if (typeof item.proxy === 'string' && item.proxy.trim() !== '') {
                const proxyName = item.proxy.trim();
                // 检查是否是已存在于commonProxies中的代理组名称
                if (commonProxies.includes(proxyName)) {
                    // 如果是，使用过滤后的代理列表（将指定的代理放在首位）
                    proxyList = [proxyName, ...commonProxies.filter(p => p !== proxyName)];
                } else {
                    // 如果不是，将其添加到列表首位
                    proxyList = [proxyName, ...commonProxies];
                }
            } else {
                // 默认使用所有代理
                proxyList = commonProxies;
            }

            ruleGroups.push({
                ...this.config.proxyGroupOptions,
                name: item.name,
                type: "select",
                proxies: proxyList,
                icon: this.iconManager.getIcon(item.icon || item.name)
            });
        }

        // 简化特殊分组
        const specialGroups = [
            {
                ...this.config.proxyGroupOptions,
                name: pgNames.GLOBAL_DIRECT,
                type: "select",
                proxies: ["DIRECT", pgNames.AUTO_SELECT],
                icon: this.iconManager.getIcon("Direct")
            },
            {
                ...this.config.proxyGroupOptions,
                name: pgNames.AD_BLOCK,
                type: "select",
                proxies: ["REJECT", "DIRECT"],
                icon: this.iconManager.getIcon("AdBlack")
            },
            {
                ...this.config.proxyGroupOptions,
                name: pgNames.FINAL,
                type: "select",
                proxies: commonProxies,
                icon: this.iconManager.getIcon("Final")
            }
        ];

        return [
            ...baseGroups,
            ...ruleGroups,
            ...specialGroups,
            ...regionGroups
        ];
    }
}

/**
 * Clash配置生成器类
 */
class ClashConfigGenerator {
    /**
     * 创建Clash配置生成器
     * @param {UserConfig} userConfig - 用户配置
     */
    constructor(userConfig = {}) {
        try {
            // 验证用户配置
            this._validateUserConfig(userConfig);

            // 合并配置
            this.config = utils.mergeObjects(defaultConfig, userConfig);

            // 初始化管理器
            this.iconManager = new IconManager(this.config);
            this.ruleManager = new RuleManager(this.config, this.iconManager);
            this.proxyGroupManager = new ProxyGroupManager(this.config, this.iconManager);

            // 预加载图标
            this.iconManager.preloadCommonIcons();

            console.log('[Clash Generator] 初始化完成');
        } catch (error) {
            console.error('[Clash Generator] 初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 验证用户配置
     * @param {UserConfig} userConfig - 用户配置
     * @private
     */
    _validateUserConfig(userConfig) {
        if (!userConfig || typeof userConfig !== 'object') {
            throw new Error('用户配置必须是一个对象');
        }

        // 验证规则提供者配置
        if (userConfig.ruleProviders && !Array.isArray(userConfig.ruleProviders)) {
            throw new Error('ruleProviders 必须是数组');
        }

        // 验证自定义规则
        if (userConfig.customRules && !Array.isArray(userConfig.customRules)) {
            throw new Error('customRules 必须是数组');
        }

        // 验证排除正则
        if (userConfig.excludeRegex && typeof userConfig.excludeRegex !== 'string') {
            throw new Error('excludeRegex 必须是字符串');
        }

        console.log('[Clash Generator] 用户配置验证通过');
    }

    /**
     * 诊断配置问题
     * @param {Object} clashConfig - Clash配置
     * @returns {Object} 诊断结果
     */
    diagnose(clashConfig) {
        const issues = [];
        const warnings = [];
        const info = [];

        try {
            // 检查代理节点
            if (!clashConfig || !Array.isArray(clashConfig.proxies)) {
                issues.push('缺少代理节点配置');
            } else if (clashConfig.proxies.length === 0) {
                warnings.push('代理节点列表为空');
            } else {
                info.push(`发现 ${clashConfig.proxies.length} 个代理节点`);
            }

            // 检查DNS配置
            if (this.config.dns && this.config.dns.enable) {
                info.push('DNS功能已启用');
                if (this.config.dns['enhanced-mode'] === 'fake-ip') {
                    info.push('使用fake-ip模式');
                }
            } else {
                warnings.push('DNS功能未启用');
            }

            // 检查规则提供者
            const ruleProviders = this.config.ruleProviders || [];
            if (ruleProviders.length === 0) {
                warnings.push('未配置规则提供者');
            } else {
                info.push(`配置了 ${ruleProviders.length} 个规则提供者`);
            }

            // 检查自定义规则
            const customRules = this.config.customRules || [];
            if (customRules.length > 0) {
                info.push(`配置了 ${customRules.length} 条自定义规则`);
            }

            return {
                issues,
                warnings,
                info,
                summary: {
                    hasIssues: issues.length > 0,
                    hasWarnings: warnings.length > 0,
                    isHealthy: issues.length === 0 && warnings.length <= 1
                }
            };
        } catch (error) {
            issues.push(`诊断过程出错: ${error.message}`);
            return { issues, warnings: [], info: [], summary: { hasIssues: true, hasWarnings: false, isHealthy: false } };
        }
    }

    /**
     * 生成Clash配置
     * @param {Object} clashConfig - 原始Clash配置
     * @returns {Object} 处理后的Clash配置
     */
    generate(clashConfig) {
        try {
            // 输入验证
            if (!clashConfig || typeof clashConfig !== 'object') {
                console.warn('[Clash Generator] 输入配置无效，使用空配置');
                clashConfig = {};
            }

            // 提取代理列表
            const proxies = Array.isArray(clashConfig.proxies) ? clashConfig.proxies : [];
            console.log(`[Clash Generator] 发现 ${proxies.length} 个代理节点`);

            // 生成各个组件
            const regionGroups = this.proxyGroupManager.createRegionGroups(proxies);
            console.log(`[Clash Generator] 创建了 ${regionGroups.length} 个区域分组`);

            const allProxyGroups = this.proxyGroupManager.createBaseProxyGroups(regionGroups);
            console.log(`[Clash Generator] 创建了 ${allProxyGroups.length} 个代理组`);

            const ruleProviders = this.ruleManager.createRuleProviders();
            console.log(`[Clash Generator] 创建了 ${Object.keys(ruleProviders).length} 个规则提供者`);

            const rules = this.ruleManager.generateRules();
            console.log(`[Clash Generator] 生成了 ${rules.length} 条规则`);

            // 构建最终配置
            const result = Object.assign({}, clashConfig, {
                dns: this.config.dns,
                "rule-providers": ruleProviders,
                rules: rules,
                "proxy-groups": allProxyGroups
            });

            console.log('[Clash Generator] 配置生成成功');
            return result;

        } catch (error) {
            console.error('[Clash Generator] 配置生成失败:', error.message);
            console.error('[Clash Generator] 错误堆栈:', error.stack);
            return clashConfig || {};
        }
    }
}

/**
 * 主函数 - 处理配置
 * @param {Object} config - 原始配置
 * @returns {Object} 处理后的配置
 */
function main(config) {
    try {
        // 输入验证
        if (!config || typeof config !== 'object') {
            console.warn('[Clash Script] 输入配置无效，返回空配置');
            return {};
        }

        // 创建或复用生成器实例
        if (!main.generator) {
            main.generator = new ClashConfigGenerator(userConfig);
            console.log('[Clash Script] 配置生成器已初始化');
        }

        // 运行诊断
        const diagnosis = main.generator.diagnose(config);

        // 输出诊断结果
        if (diagnosis.issues.length > 0) {
            console.error('[Clash Script] 发现问题:', diagnosis.issues);
        }
        if (diagnosis.warnings.length > 0) {
            console.warn('[Clash Script] 警告:', diagnosis.warnings);
        }
        if (diagnosis.info.length > 0) {
            console.log('[Clash Script] 信息:', diagnosis.info);
        }

        // 生成配置
        const result = main.generator.generate(config);
        console.log('[Clash Script] 配置生成完成');

        return result;
    } catch (error) {
        console.error('[Clash Script] 配置生成失败:', error.message);
        console.error('[Clash Script] 错误详情:', error.stack);
        // 返回原配置以确保基本功能
        return config || {};
    }
}

// 导出诊断功能供外部使用
main.diagnose = function(config) {
    try {
        if (!main.generator) {
            main.generator = new ClashConfigGenerator(userConfig);
        }
        return main.generator.diagnose(config);
    } catch (error) {
        return {
            issues: [`诊断失败: ${error.message}`],
            warnings: [],
            info: [],
            summary: { hasIssues: true, hasWarnings: false, isHealthy: false }
        };
    }
};
