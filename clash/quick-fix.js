/**
 * Clash DNS问题快速修复脚本
 * 一键解决常见的DNS解析失败问题
 */

// 快速修复配置
const quickFixConfig = {
    // 最稳定的DNS配置
    dns: {
        "enable": true,
        "listen": "127.0.0.1:1053",
        "ipv6": false,                    // 禁用IPv6避免问题
        "use-system-hosts": true,
        "cache-algorithm": "arc",
        "enhanced-mode": "redir-host",    // 使用兼容性最好的模式
        "fake-ip-range": "**********/16",
        "fake-ip-filter": [
            // 扩展的fake-ip过滤列表
            "+.lan", "+.local", "+.localhost", "+.corp", "+.home", "+.internal",
            "+.msftconnecttest.com", "+.msftncsi.com", 
            "+.microsoft.com", "+.microsoftonline.com", "+.office.com", "+.live.com",
            "+.jetbrains.com", "+.intellij.net",
            "+.apple.com", "+.icloud.com",
            "+.alipay.com", "+.paypal.com", "+.banking.com",
            "localhost.ptlogin2.qq.com", "localhost.sec.qq.com", "localhost.work.weixin.qq.com"
        ],
        "default-nameserver": [
            "***************",    // 最稳定的国内DNS
            "*********",          // 阿里DNS备用
            "*******"             // Cloudflare备用
        ],
        "nameserver": [
            "***************",    // 优先使用最稳定的DNS
            "*********",
            "************",       // 腾讯DNS
            "*******"             // 国外DNS最后使用
        ],
        "proxy-server-nameserver": [
            "*******",            // 代理服务器使用国外DNS
            "*******"
        ],
        "nameserver-policy": {
            // 简化的分流策略，确保稳定性
            "geosite:private,cn,geolocation-cn": [
                "***************",
                "*********"
            ],
            "geosite:gfw,geolocation-!cn": [
                "*******",
                "*******"
            ]
        }
    },

    // 优化的代理组配置
    proxyGroupOptions: {
        interval: 600,                    // 10分钟测速间隔
        timeout: 10000,                   // 10秒超时
        url: "http://www.gstatic.com/generate_204",  // 稳定的测速URL
        lazy: true,                       // 启用懒加载
        "max-failed-times": 5,            // 增加最大失败次数
        "expected-status": "204"
    },

    // 关键域名直连规则
    criticalRules: [
        // 内网和本地
        "IP-CIDR,10.0.0.0/8,DIRECT,no-resolve",
        "IP-CIDR,**********/12,DIRECT,no-resolve",
        "IP-CIDR,***********/16,DIRECT,no-resolve",
        "IP-CIDR,*********/8,DIRECT,no-resolve",
        "IP-CIDR,***********/16,DIRECT,no-resolve",
        
        // 微软服务 - 解决连接失败
        "DOMAIN-SUFFIX,microsoft.com,DIRECT",
        "DOMAIN-SUFFIX,microsoftonline.com,DIRECT",
        "DOMAIN-SUFFIX,office.com,DIRECT",
        "DOMAIN-SUFFIX,live.com,DIRECT",
        "DOMAIN-SUFFIX,msftconnecttest.com,DIRECT",
        "DOMAIN-SUFFIX,msftncsi.com,DIRECT",
        
        // JetBrains服务
        "DOMAIN-SUFFIX,jetbrains.com,DIRECT",
        "DOMAIN-SUFFIX,intellij.net,DIRECT",
        "DOMAIN-KEYWORD,jetbrains,DIRECT",
        
        // 开发工具
        "DOMAIN-SUFFIX,augmentcode.com,DIRECT",
        "DOMAIN-SUFFIX,cursor.sh,DIRECT",
        "DOMAIN-SUFFIX,cursor-cdn.com,DIRECT",
        "DOMAIN-SUFFIX,cursorapi.com,DIRECT",
        "DOMAIN-SUFFIX,cursor.com,DIRECT",
        "DOMAIN-SUFFIX,workos.com,DIRECT",
        
        // 国内常用服务
        "DOMAIN-SUFFIX,baidu.com,DIRECT",
        "DOMAIN-SUFFIX,360.net,DIRECT",
        "DOMAIN-SUFFIX,qq.com,DIRECT",
        "DOMAIN-SUFFIX,taobao.com,DIRECT",
        "DOMAIN-SUFFIX,tmall.com,DIRECT",
        "DOMAIN-SUFFIX,jd.com,DIRECT"
    ]
};

/**
 * 应用快速修复
 * @param {Object} originalConfig - 原始Clash配置
 * @returns {Object} 修复后的配置
 */
function applyQuickFix(originalConfig) {
    console.log('[Quick Fix] 开始应用DNS快速修复...');
    
    try {
        // 备份原始配置
        const backup = JSON.parse(JSON.stringify(originalConfig));
        
        // 应用修复
        const fixedConfig = Object.assign({}, originalConfig, {
            dns: quickFixConfig.dns
        });

        // 如果存在proxy-groups，更新其配置
        if (fixedConfig['proxy-groups'] && Array.isArray(fixedConfig['proxy-groups'])) {
            fixedConfig['proxy-groups'] = fixedConfig['proxy-groups'].map(group => {
                return Object.assign({}, group, quickFixConfig.proxyGroupOptions);
            });
        }

        // 添加关键规则到规则列表开头
        if (!fixedConfig.rules) {
            fixedConfig.rules = [];
        }
        
        // 将关键规则插入到最前面
        fixedConfig.rules = [
            ...quickFixConfig.criticalRules,
            ...fixedConfig.rules.filter(rule => 
                !quickFixConfig.criticalRules.includes(rule)
            )
        ];

        console.log('[Quick Fix] DNS快速修复应用完成');
        console.log('[Quick Fix] 主要改进:');
        console.log('  - 切换到redir-host模式提高兼容性');
        console.log('  - 禁用IPv6避免双栈问题');
        console.log('  - 优化DNS服务器配置');
        console.log('  - 增加超时时间到10秒');
        console.log('  - 添加关键域名直连规则');
        
        return fixedConfig;
        
    } catch (error) {
        console.error('[Quick Fix] 修复失败:', error.message);
        return originalConfig;
    }
}

/**
 * 检查配置是否需要修复
 * @param {Object} config - Clash配置
 * @returns {Object} 检查结果
 */
function checkNeedsFix(config) {
    const issues = [];
    const suggestions = [];
    
    try {
        // 检查DNS配置
        const dns = config.dns;
        if (!dns) {
            issues.push('缺少DNS配置');
        } else {
            if (dns['enhanced-mode'] === 'fake-ip') {
                suggestions.push('建议切换到redir-host模式');
            }
            if (dns.ipv6 === true) {
                suggestions.push('建议禁用IPv6');
            }
            if (!dns.nameserver || dns.nameserver.length === 0) {
                issues.push('缺少DNS服务器配置');
            }
        }

        // 检查代理组配置
        const proxyGroups = config['proxy-groups'];
        if (proxyGroups && Array.isArray(proxyGroups)) {
            const hasShortTimeout = proxyGroups.some(group => 
                group.timeout && group.timeout < 5000
            );
            if (hasShortTimeout) {
                suggestions.push('建议增加代理超时时间');
            }
        }

        // 检查规则
        const rules = config.rules;
        if (!rules || !Array.isArray(rules)) {
            issues.push('缺少路由规则');
        }

        return {
            needsFix: issues.length > 0 || suggestions.length > 0,
            issues,
            suggestions,
            severity: issues.length > 0 ? 'high' : 'medium'
        };
        
    } catch (error) {
        return {
            needsFix: true,
            issues: [`配置检查失败: ${error.message}`],
            suggestions: [],
            severity: 'high'
        };
    }
}

/**
 * 主修复函数
 * @param {Object} config - 原始配置
 * @returns {Object} 修复后的配置
 */
function main(config) {
    console.log('[Quick Fix] Clash DNS问题快速修复工具');
    console.log('[Quick Fix] 版本: 1.0.0');
    
    // 检查是否需要修复
    const checkResult = checkNeedsFix(config);
    
    if (!checkResult.needsFix) {
        console.log('[Quick Fix] 配置看起来正常，无需修复');
        return config;
    }

    console.log('[Quick Fix] 检测到以下问题:');
    checkResult.issues.forEach(issue => {
        console.log(`  ❌ ${issue}`);
    });
    
    console.log('[Quick Fix] 优化建议:');
    checkResult.suggestions.forEach(suggestion => {
        console.log(`  💡 ${suggestion}`);
    });

    // 应用修复
    return applyQuickFix(config);
}

// 导出功能
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        main,
        applyQuickFix,
        checkNeedsFix,
        quickFixConfig
    };
}

// 浏览器环境
if (typeof window !== 'undefined') {
    window.ClashQuickFix = {
        main,
        applyQuickFix,
        checkNeedsFix
    };
}
